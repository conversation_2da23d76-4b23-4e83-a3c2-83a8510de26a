import { create } from 'zustand';

interface State {
  isOpen: boolean;
  component: React.ReactNode;
}

interface Action {
  setIsOpen: (isOpen: boolean) => void;
  setComponent: (component: React.ReactNode) => void;
}

const useResponsiveDialogStore = create<State & Action>(set => ({
  isOpen: false,
  component: null,
  setIsOpen: (isOpen: boolean) => set({ isOpen }),
  setComponent: (component: React.ReactNode) => set({ component }),
}));

export default useResponsiveDialogStore;
