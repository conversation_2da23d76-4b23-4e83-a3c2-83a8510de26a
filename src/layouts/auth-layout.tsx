import { Outlet } from 'react-router';
import MobileTabsNavigation from '../components/navigation/mobile-tabs-navigation';
import DynamicDrawer from '@/components/dynamic-drawer';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/navigation/app-sidebar';
import { SiteHeader } from '@/components/navigation/site-header';

const NavigationLayout = () => {
  return (
    <main className="custom-scrollbar">
      <SidebarProvider className="h-full">
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader />
          <div className="h-full flex-1 overflow-hidden relative">
            <div className="h-full absolute top-0 left-0 right-0 bottom-0 overflow-auto overscroll-none no-scrollbar">
              <Outlet />
            </div>
          </div>
          <MobileTabsNavigation />
        </SidebarInset>
      </SidebarProvider>

      <DynamicDrawer dragOnlyHandle />
    </main>
  );
};

export default NavigationLayout;
