import UsdcLogo from '@/assets/logos/usdc.svg';

export function getSpotTokenImage(token: string) {
  if (token === 'USDC') {
    return UsdcLogo;
  }

  if (token.includes('USD')) {
    return `https://app.hyperliquid.xyz/coins/${token}_USDC.svg`;
  }

  if (token.startsWith('U')) {
    const tokenFormat = token.slice(1);
    return `https://app.hyperliquid.xyz/coins/${tokenFormat}_USDC.svg`;
  }

  return `https://app.hyperliquid.xyz/coins/${token}_USDC.svg`;
}


export function calculateCandleTimeRange(timeFrame: '1h' | '24h' | '1w' | '1m' | 'ytd' | 'all', tokenLaunchDate?: number) {
  switch (timeFrame) {
    case '1h': {
      return {
        interval: '1m',
        startTime: Date.now() - 60 * 60 * 1000,
        endTime: Date.now(),
      }
    }
    case '24h': {
      return {
        interval: '15m',
        startTime: Date.now() - 24 * 60 * 60 * 1000,
        endTime: Date.now(),
      }
    }
    case '1w': {
      return {
        interval: '15m',
        startTime: Date.now() - 7 * 24 * 60 * 60 * 1000,
        endTime: Date.now(),
      }
    }
    case '1m': {
      return {
        interval: '1d',
        startTime: Date.now() - 30 * 24 * 60 * 60 * 1000,
        endTime: Date.now(),
      }
    }
    case 'ytd': {
      return {
        interval: '1d',
        startTime: new Date(new Date().getFullYear(), 0, 1).getTime(),
        endTime: Date.now()
      }
    }
    case 'all': {
      return {
        interval: '1d',
        startTime: tokenLaunchDate ? tokenLaunchDate : 0,
        endTime: Date.now()
      }
    }
  }

}