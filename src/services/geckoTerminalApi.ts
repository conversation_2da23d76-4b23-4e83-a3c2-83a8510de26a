import { ENDPOINTS } from './endpoints';

// https://www.geckoterminal.com/dex-api

export type Network = 'hyperliquid' | 'hyperevm';

export const fetchTrendingPools = async (networkId: Network, page: number) => {
  const response = await fetch(
    `${ENDPOINTS.GECKO_TERMINAL}/networks/${networkId}/trending_pools?page=${page}&duration=24h`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json;version=20230302',
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchTopPoolsByDex = async (networkId: Network, dex: string, page: number, sort:
  'h24_tx_count_desc' | 'h24_volume_usd_desc'
) => {
  const response = await fetch(
    `${ENDPOINTS.GECKO_TERMINAL}/networks/${networkId}/dexes/${dex}/pools?page=${page}&sort=${sort}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json;version=20230302',
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchHyperEvmTokenPrices = async (addresses: string[]) => {
  const response = await fetch(
    `${ENDPOINTS.GECKO_TERMINAL}/simple/networks/hyperevm/token_price/${addresses}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json;version=20230302',
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchPoolTokenInfo = async (networkId: Network, poolAddress: string) => {
  const response = await fetch(
    `${ENDPOINTS.GECKO_TERMINAL}/networks/${networkId}/pools/${poolAddress}/info`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json;version=20230302',
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};

export const fetchTopPoolsForAToken = async (networkId: Network, tokenAddress: string) => {
  const response = await fetch(
    `${ENDPOINTS.GECKO_TERMINAL}/networks/${networkId}/tokens/${tokenAddress}/pools`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json;version=20230302',
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Network response was not ok: ${response.status}`);
  }

  return await response.json();
};
