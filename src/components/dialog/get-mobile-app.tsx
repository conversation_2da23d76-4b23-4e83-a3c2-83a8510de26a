import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { cn } from "@/lib/utils";

const GetMobileApp = () => {
    const [isIOS, setIsIOS] = useState(true);

    return (
        <div className="flex flex-col h-full max-h-[80vh]">
            {/* Header - Fixed */}
            <div className="flex-shrink-0 p-4 pb-2 sticky top-0 z-10 border-b">
                <h2 className="text-xl font-semibold text-center mb-4">
                    Get Purro Wallet on Mobile
                </h2>

                {/* Platform Toggle Buttons - Always visible */}
                <div className="flex justify-center gap-3">
                    <Button
                        onClick={() => setIsIOS(true)}
                        className={cn(
                            'flex-1 max-w-[120px] transition-all',
                            isIOS
                                ? "bg-primary text-primary-foreground"
                                : "bg-accent hover:bg-accent/80"
                        )}
                    >
                        iOS
                    </Button>
                    <Button
                        onClick={() => setIsIOS(false)}
                        className={cn(
                            'flex-1 max-w-[120px] transition-all',
                            !isIOS
                                ? "bg-primary text-primary-foreground"
                                : "bg-accent hover:bg-accent/80"
                        )}
                    >
                        Android
                    </Button>
                </div>
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4 pt-2 pb-20 md:pb-0 custom-scrollbar">
                {isIOS && (
                    <div className="space-y-4">
                        <h3 className="font-medium">Install on iOS:</h3>
                        <ol className="list-decimal pl-5 space-y-4">
                            <li>
                                <div>
                                    <p>Open Safari and visit <span className="font-medium">purro.xyz</span></p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/ios-install-1.jpg"
                                            alt="Open Safari and visit purro.xyz"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Tap the share icon at the bottom of the screen</p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/ios-install-2.jpg"
                                            alt="Tap the share icon"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Scroll down and tap <span className="font-medium">Add to Home Screen</span></p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/ios-install-3.jpg"
                                            alt="Tap Add to Home Screen"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Tap <span className="font-medium">Add</span> in the top right corner</p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/ios-install-4.jpg"
                                            alt="Tap Add in the top right corner"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                        </ol>
                    </div>
                )}

                {!isIOS && (
                    <div className="space-y-4">
                        <h3 className="font-medium">Install on Android:</h3>
                        <ol className="list-decimal pl-5 space-y-4">
                            <li>
                                <div>
                                    <p>Open Chrome and visit <span className="font-medium">purro.xyz</span></p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/android-install-1.png"
                                            alt="Open Chrome and visit purro.xyz"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Tap the menu icon (three dots) in the top right</p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/android-install-2.png"
                                            alt="Tap the menu icon"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Tap <span className="font-medium">Install app</span> or <span className="font-medium">Add to Home screen</span></p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/android-install-3.png"
                                            alt="Tap Install app"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div>
                                    <p>Follow the on-screen instructions to complete installation</p>
                                    <div className="mt-2 rounded-lg overflow-hidden border border-border">
                                        <img
                                            src="/docs/android-install-4.png"
                                            alt="Follow on-screen instructions"
                                            className="mx-auto w-auto h-auto max-w-[300px]"
                                        />
                                    </div>
                                </div>
                            </li>
                        </ol>
                    </div>
                )}
            </div>
        </div>
    );
};

export default GetMobileApp;