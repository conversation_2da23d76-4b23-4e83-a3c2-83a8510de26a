
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { StillBuilding } from '@/components/common/still-building'
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";


const BuildingDialog = (
    {
        isOpen,
        onOpenChange,
    }: {
        isOpen: boolean;
        onOpenChange: (open: boolean) => void;
    }
) => {
    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
                <VisuallyHidden>
                    <DialogHeader>
                        <DialogTitle>Building</DialogTitle>
                    </DialogHeader>
                </VisuallyHidden>
                <StillBuilding />
            </DialogContent>
        </Dialog>
    )
}

export default BuildingDialog