'use client';

import * as React from 'react';
import { LucideIcon, Smartphone } from 'lucide-react';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { Link, useLocation } from 'react-router';
import { cn } from '@/lib/utils';
import useResponsiveDialogStore from '@/store/responsiveDialog';
import GetMobileApp from '../dialog/get-mobile-app';

export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon | React.FC;
  }[];
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const { pathname } = useLocation();
  const isActive = (url: string) => (url === '/' ? pathname === '/' : pathname.startsWith(url));
  const { setIsOpen, setComponent } = useResponsiveDialogStore();
  const handleGetMobileApp = () => {
    setIsOpen(true);
    setComponent(<GetMobileApp />);
  }

  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map(item => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild>
                <Link
                  to={item.url}
                  target={item.url.startsWith('http') ? '_blank' : '_self'}
                  className={cn(
                    'hover:bg-accent flex cursor-pointer items-center gap-2 rounded-3xl py-2 pr-2 pl-3 transition-colors duration-200 ease-linear',
                    isActive(item.url) && '!bg-primary/10 text-accent-foreground'
                  )}
                >
                  <item.icon className={cn(isActive(item.url) && "text-primary")} />
                  <span className={cn(isActive(item.url) && "text-primary")}>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
          <SidebarMenuItem>
            <SidebarMenuButton onClick={handleGetMobileApp} className='hover:bg-accent flex cursor-pointer items-center gap-2 rounded-3xl py-2 pr-2 pl-3 transition-colors duration-200 ease-linear'>
              <Smartphone />
              <span>Get Mobile App</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup >
  );
}
