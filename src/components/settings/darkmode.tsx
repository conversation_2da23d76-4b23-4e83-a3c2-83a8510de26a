import { Theme, useTheme } from "@/providers/theme-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Moon, Sun, Monitor } from "lucide-react"
import { useState } from "react"

const Darkmode = () => {
    const { setTheme, theme } = useTheme()
    const [isOpen, setIsOpen] = useState(false)

    const themes = [
        { value: 'light', label: 'Light', icon: Sun },
        { value: 'dark', label: 'Dark', icon: Moon },
        { value: 'system', label: 'System', icon: Monitor }
    ]

    const getCurrentIcon = () => {
        const currentTheme = themes.find(t => t.value === theme)
        const Icon = currentTheme?.icon || Sun
        return <Icon className="h-[1.2rem] w-[1.2rem]" />
    }

    const handleSelect = (value: Theme) => {
        setTheme(value)
        setIsOpen(false)
    }

    return (
        <div className="w-full">
            <div className="flex items-start justify-between gap-5">
                <div>
                    <p className="font-semibold">Dark Mode</p>
                    <span className="text-sm text-muted-foreground">
                        Switch between light and dark mode
                    </span>
                </div>

                <div className="relative">
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setIsOpen(!isOpen)}
                        className="relative"
                    >
                        {getCurrentIcon()}
                    </Button>

                    {isOpen && (
                        <div className="absolute right-0 top-full mt-2 w-32 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            {themes.map((themeOption) => {
                                const Icon = themeOption.icon
                                return (
                                    <button
                                        key={themeOption.value}
                                        onClick={() => handleSelect(themeOption.value as Theme)}
                                        className={`w-full px-3 py-2 text-left flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 first:rounded-t-md last:rounded-b-md transition-colors ${theme === themeOption.value ? 'bg-gray-100 dark:bg-gray-700' : ''
                                            }`}
                                    >
                                        <Icon className="h-4 w-4" />
                                        {themeOption.label}
                                    </button>
                                )
                            })}
                        </div>
                    )}
                </div>
            </div>

            {/* Overlay to close dropdown when clicking outside */}
            {isOpen && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => setIsOpen(false)}
                />
            )}
        </div>
    )
}

export default Darkmode