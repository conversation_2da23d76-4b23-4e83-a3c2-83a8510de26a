import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import useResponsiveDialogStore from '@/store/responsiveDialog';
import GetMobileApp from '../dialog/get-mobile-app';

// Define window extensions
interface NavigatorWithStandalone extends Navigator {
  standalone?: boolean;
}

export function InstallPrompt() {
  const [isStandalone, setIsStandalone] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isMobileDevice, setIsMobileDevice] = useState(false);

  useEffect(() => {
    const detectMobile = () => {
      const userAgent = navigator.userAgent || '';

      // General mobile detection
      const isMobile = /iPhone|iPad|iPod|Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(
        userAgent
      );
      setIsMobileDevice(isMobile);

      // Check if already installed as PWA
      const navigatorWithStandalone = window.navigator as NavigatorWithStandalone;
      const isInStandaloneMode =
        window.matchMedia('(display-mode: standalone)').matches ||
        navigatorWithStandalone.standalone ||
        document.referrer.includes('android-app://');
      setIsStandalone(isInStandaloneMode);
    };

    detectMobile();

    // Show prompt if not installed and on mobile device
    if (!isStandalone && isMobileDevice) {
      const timer = setTimeout(() => {
        setShowPrompt(true);
      }, 2000);
      return () => clearTimeout(timer);
    }

    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleChange = (e: MediaQueryListEvent) => {
      setIsStandalone(e.matches);
      if (e.matches) setShowPrompt(false);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [isStandalone, isMobileDevice]);

  const dismissPrompt = () => {
    setShowPrompt(false);
  };

  const { setIsOpen, setComponent } = useResponsiveDialogStore();

  const handleShowInstructions = () => {
    setIsOpen(true);
    setComponent(<GetMobileApp />);
  };

  // Don't render anything if already installed or not a mobile device
  if (isStandalone || !isMobileDevice) {
    return null;
  }

  return (
    <>
      {showPrompt && (
        <div className="bg-background fixed top-0 right-0 left-0 z-[100] w-full border-b shadow-sm">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 flex h-9 w-9 items-center justify-center overflow-hidden rounded-full">
                <img
                  src="/apple-touch-icon.png"
                  alt="Purro logo"
                  width={24}
                  height={24}
                  className="size-8 rounded-full object-cover"
                  onError={e => {
                    e.currentTarget.style.display = 'none';
                    if (e.currentTarget.parentElement) {
                      e.currentTarget.parentElement.innerHTML = '📱';
                    }
                  }}
                />
              </div>
              <div>
                <h3 className="text-base font-semibold">Purro</h3>
                <p className="text-muted-foreground text-sm">Install application</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="default"
                size="sm"
                className="h-8 rounded-full px-4 text-sm"
                onClick={handleShowInstructions}
              >
                Install
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={dismissPrompt}>
                <X size={16} />
              </Button>
            </div>
          </div>

          {/* The dialog is now handled by the ResponsiveDialog component */}
          {/* The GetMobileApp component will be rendered inside the responsive dialog */}
        </div>
      )}
    </>
  );
}
