import { ChartConfig, Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "../ui/chart"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"

interface TokenSimpleChartProps {
    chartData: unknown[];
    chartDomain: number[];
    isUpTrend: boolean;
}

// Chart config
const chartConfig = {
    price: {
        label: "Price",
        color: "#088b88",
    },
} satisfies ChartConfig;

const TokenSimpleChart = ({ chartData, chartDomain, isUpTrend }: TokenSimpleChartProps) => {
    return (
        <ChartContainer config={chartConfig} className="h-full area-chart-wrapper">
            <AreaChart
                accessibilityLayer
                data={chartData}
                margin={{
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                }}
                className='w-full h-full'
            >
                <CartesianGrid stroke="transparent" />
                <XAxis
                    dataKey="time"
                    tickLine={false}
                    axisLine={false}
                    tick={false}
                    height={0}
                    interval="preserveStartEnd"
                />

                <ChartTooltip
                    cursor={{ stroke: 'var(--border)', strokeWidth: 1 }}
                    content={<ChartTooltipContent
                        indicator="dot"
                        labelFormatter={(value) => `${value}`}
                        formatter={(value) => [
                            `$${Number(value).toLocaleString()}`
                        ]}
                    />}
                />
                <Area
                    dataKey="price"
                    type="monotone"
                    fill="transparent"
                    fillOpacity={0}
                    stroke={isUpTrend ? "oklch(62.7% 0.194 149.214)" : "oklch(63.7% 0.237 25.331)"}
                    strokeWidth={3}
                    yAxisId="price"
                />
                <YAxis
                    yAxisId="price"
                    domain={chartDomain}
                    hide
                />
                <YAxis
                    yAxisId="price-display"
                    orientation="right"
                    domain={chartDomain}
                    axisLine={false}
                    tickLine={false}
                    tickCount={10}
                    tickFormatter={(value) => `$${Number(value).toLocaleString()}`}
                    tick={{
                        fontSize: 12,
                        fill: 'var(--foreground)'
                    }}
                />
            </AreaChart>
        </ChartContainer>
    )
}

export default TokenSimpleChart