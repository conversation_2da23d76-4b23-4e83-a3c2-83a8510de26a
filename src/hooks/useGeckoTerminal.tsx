import { useQuery } from '@tanstack/react-query';
import { fetchTopPoolsByDex, fetchTopPoolsForAToken, fetchTrendingPools } from '../services/geckoTerminalApi';
import { Network } from '../services/geckoTerminalApi';
import QueryKeys from '@/constants/queryKeys';

export const useGeckoTerminal = () => {
  const useTrendingPools = (networkId: Network, page: number) => {
    return useQuery({
      queryKey: [QueryKeys.TRENDING_POOLS, networkId, page],
      queryFn: () => fetchTrendingPools(networkId, page),
      staleTime: 30 * 1000, // 3 seconds
      refetchInterval: 10 * 1000, // 10 seconds
    });
  };

  const useTopPoolsByDex = (networkId: Network, dex: string, page: number, sort: 'h24_tx_count_desc' | 'h24_volume_usd_desc') => {
    return useQuery({
      queryKey: [QueryKeys.TOP_POOLS_BY_DEX, networkId, dex, page, sort],
      queryFn: () => fetchTopPoolsByDex(networkId, dex, page, sort),
      staleTime: 30 * 1000,
      refetchInterval: 10 * 1000,
      // Đảm bảo fetch lại khi các tham số thay đổi
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      // Điều này đảm bảo query luôn được coi là mới khi dex thay đổi
      enabled: !!dex,
    });
  };

  const useTopPoolsForAToken = (networkId: Network, tokenAddress: string) => {
    return useQuery({
      queryKey: [QueryKeys.TOP_POOLS_FOR_A_TOKEN, networkId, tokenAddress],
      queryFn: () => fetchTopPoolsForAToken(networkId, tokenAddress),
      staleTime: 30 * 1000,
      refetchInterval: 10 * 1000,
      // Đảm bảo fetch lại khi các tham số thay đổi
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      // Điều này đảm bảo query luôn được coi là mới khi dex thay đổi
      enabled: !!tokenAddress,
    });
  };


  return {
    useTrendingPools,
    useTopPoolsByDex,
    useTopPoolsForAToken,
  };
};
