import { DynamicContextProvider, mergeNetworks } from '@dynamic-labs/sdk-react-core';
import { DynamicWagmiConnector } from '@dynamic-labs/wagmi-connector';
import { createConfig, WagmiProvider } from 'wagmi';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { http } from 'viem';
import { arbitrum, base, mainnet, bsc } from 'viem/chains';
import { EthereumWalletConnectors } from '@dynamic-labs/ethereum';
import { useTheme } from './theme-provider';
import { GlobalWalletExtension } from '@dynamic-labs/global-wallet';
import { hyperEvm } from './customChains';

const evmNetworks = [
  {
    blockExplorerUrls: ['https://hyperscan.gas.zip/'],
    chainId: 999,
    chainName: 'HyperEVM',
    iconUrls: ['https://i.postimg.cc/WtRJRmq4/hyperliquid.png'],
    name: 'Hyper<PERSON><PERSON><PERSON>',
    nativeCurrency: {
      decimals: 18,
      name: 'Hyperliquid',
      symbol: 'HYPE',
      iconUrl: 'https://i.postimg.cc/WtRJRmq4/hyperliquid.png',
    },
    networkId: 999,
    rpcUrls: ['https://rpc.hyperliquid.xyz/evm'],
    vanityName: 'HyperEVM',
  },
];

const config = createConfig({
  chains: [hyperEvm, mainnet, base, arbitrum, bsc],
  multiInjectedProviderDiscovery: false,
  transports: {
    [mainnet.id]: http(),
    [hyperEvm.id]: http(),
    [base.id]: http(),
    [arbitrum.id]: http(),
    [bsc.id]: http(),
  },
});

const queryClient = new QueryClient();

const locale = {
  en: {
    dyn_login: {
      title: {
        all: '',
        all_wallet_list: 'Find your favorite',
      },
    },
  },
};

export default function DynamicProvider({ children }: { children: React.ReactNode }) {
  const clientId = import.meta.env.VITE_PUBLIC_DYNAMIC_CLIENT_ID;
  if (!clientId) {
    throw new Error('VITE_PUBLIC_DYNAMIC_CLIENT_ID is not defined');
  }
  const { theme } = useTheme();

  const themeValue = theme === 'system' ? 'auto' : theme;

  return (
    <DynamicContextProvider
      theme={themeValue}
      settings={{
        environmentId: clientId,
        walletConnectors: [
          EthereumWalletConnectors
        ],
        overrides: {
          evmNetworks: networks => mergeNetworks(evmNetworks, networks),
        },
        cssOverrides: `
                .popper-content {
                    left: 150px !important;
                }
                .footer-options-switcher__container {
                    padding: .75rem 0;
                }
                @media (max-width: 768px) {
                    .footer-options-switcher__container {
                        padding-bottom: 2rem !important;
                    }
                }
                .wallet-icon-with-network__container svg path {
                    fill: var(--color-primary);
                }
                .settings-view__body__section__button__icon rect {
                    fill: var(--color-primary);
                }
                .send-balance-widget-view {
                    max-width: 100%;
                }
                .menu-list__overlay-card__container {
                    padding-bottom: 2rem;
                }
                `,
        walletConnectorExtensions: [GlobalWalletExtension],
        termsOfServiceUrl: 'https://purro.xyz/terms',
        privacyPolicyUrl: 'https://purro.xyz/privacy',
      }}
      locale={locale}
    >
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <DynamicWagmiConnector>{children}</DynamicWagmiConnector>
        </QueryClientProvider>
      </WagmiProvider>
    </DynamicContextProvider>
  );
}
