import { createRoot } from 'react-dom/client';
import './index.css';
import DynamicProvider from './providers/dynamic-provider.tsx';
import AppRoutes from './routes/index.tsx';
import ThemeProvider from './providers/theme-provider.tsx';
import AdditionalProvider from './providers/additional-provider.tsx';
import { BrowserRouter } from 'react-router';
import PrivyGlobalProvider from './providers/privy-provider.tsx';

createRoot(document.getElementById('root')!).render(
  <ThemeProvider>
    <BrowserRouter>
      <DynamicProvider>
        <PrivyGlobalProvider>
          <AdditionalProvider>
            <AppRoutes />
          </AdditionalProvider>
        </PrivyGlobalProvider>
      </DynamicProvider>
    </BrowserRouter>
  </ThemeProvider>
);
