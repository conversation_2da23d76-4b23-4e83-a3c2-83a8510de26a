import HyperliquidLogo from "@/assets/logos/hyperliquid_logo.svg";
import Hypurr<PERSON><PERSON><PERSON>ogo from "@/assets/logos/hypurrfun.png";
import HyperSwapLogo from "@/assets/logos/hyperswap.svg";
import Liquina<PERSON>ogo from "@/assets/logos/liquina.avif";

const exploreLinks = [
    {
        name: "HyperLiquid",
        logo: Hyperliquid<PERSON>ogo,
        href: "https://hyperfoundation.org",
        description: "HyperLiquid is a decentralized exchange (DEX) and liquidity protocol built on the Hyperliquid protocol.",

    },
    {
        name: "HypurrFun",
        logo: Hypurr<PERSON>un<PERSON>ogo,
        href: "https://hypurr.fun",
        description: "Launch and trade memecoins on Hyperliquid with a few clicks, directly from Telegram. Snipe new launches, access whale chats, and compete with other cabals.",

    },
    {
        name: "HyperSwap",
        logo: HyperSwap<PERSON>ogo,
        href: "https://hyperswap.exchange",
        description: "HyperSwap is the first native DEX for the HyperEVM. HyperSwap is deeply integrated across the HyperEVM with most of the top protocols, tokens, and liquidity providers in the ecosystem.",

    },
    {
        name: "Liquina",
        logo: LiquinaLogo,
        href: "https://liquina.ai",
        description: "The Queen of Hyperliquid.",

    }
]

export default exploreLinks