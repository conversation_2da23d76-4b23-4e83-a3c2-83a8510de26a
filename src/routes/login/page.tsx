import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Favicon from '@/assets/logos/icon-transparent.png';
import { useLoginWithOAuth, useLoginWithEmail, useLogin, usePrivy } from '@privy-io/react-auth';
import XIcon from '@/assets/icons/XIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import WalletIcon from '@/assets/icons/WalletIcon';
import { useNavigate } from 'react-router';
import { useEffect, useState } from 'react';
import { ArrowLeft } from 'lucide-react';

const LoginPage = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [emailFlowKey, setEmailFlowKey] = useState(0); // Key to force re-render

  const { ready, authenticated } = usePrivy();
  const { login } = useLogin();

  const { state: oauthState, initOAuth } = useLoginWithOAuth({
    onComplete: ({ user, isNewUser }) => {
      console.log('User logged in successfully via OAuth', user);
      if (isNewUser) {
        console.log('Welcome new user!');
      }
    },
    onError: error => {
      console.error('OAuth login failed', error);
    },
  });

  const {
    state: emailState,
    sendCode,
    loginWithCode,
  } = useLoginWithEmail({
    onComplete: ({ user, isNewUser }) => {
      console.log('User logged in successfully via email', user);
      if (isNewUser) {
        console.log('Welcome new user!');
      }
    },
    onError: error => {
      console.error('Email login failed', error);
    },
  });

  // Redirect to home page when login is successful
  useEffect(() => {
    if (oauthState.status === 'done' || emailState.status === 'done') {
      navigate('/');
    }
  }, [oauthState.status, emailState.status, navigate]);

  // Check if any process is loading
  const isEmailLoading =
    emailState.status === 'sending-code' || emailState.status === 'submitting-code';
  const isOAuthLoading = oauthState.status === 'loading';
  const disableWalletLogin = !ready || (ready && authenticated);

  const handleOAuthLogin = async (provider: 'google' | 'twitter' | 'discord') => {
    try {
      await initOAuth({ provider });
    } catch (err) {
      console.error(`${provider} login failed:`, err);
    }
  };

  const handleWalletLogin = () => {
    try {
      login({
        loginMethods: ['wallet'],
        walletChainType: 'ethereum-and-solana',
        disableSignup: false,
      });
    } catch (err) {
      console.error('Wallet login failed:', err);
    }
  };

  const handleSendCode = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    try {
      await sendCode({ email: email.trim() });
    } catch (err) {
      console.error('Send code failed:', err);
    }
  };

  const handleLoginWithCode = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!code.trim()) return;

    try {
      await loginWithCode({ code: code.trim() });
    } catch (err) {
      console.error('Login with code failed:', err);
    }
  };

  // Remove the old isLoading variable since we have specific loading states now

  return (
    <div className="bg-background flex min-h-svh flex-col items-center justify-center gap-6 p-3 md:p-10">
      {/* Back to Home Button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 left-4"
        onClick={() => navigate('/')}
        title="Back to Home"
      >
        <ArrowLeft className="size-4" />
      </Button>

      <div className="w-full max-w-sm">
        <div className={cn('flex flex-col gap-6')}>
          <div className="flex flex-col gap-6">
            <div className="flex flex-col items-center gap-2">
              <a href="#" className="flex flex-col items-center gap-2 font-medium">
                <div className="flex size-8 items-center justify-center rounded-md">
                  <img className="size-8" src={Favicon} />
                </div>
                <span className="sr-only">Purro</span>
              </a>
              <h1 className="text-xl font-bold">Welcome to Purro.</h1>
            </div>
            <div key={emailFlowKey} className="flex flex-col gap-6">
              {emailState.status !== 'awaiting-code-input' ? (
                <form onSubmit={handleSendCode}>
                  <div className="grid gap-3">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      required
                      disabled={isEmailLoading}
                    />
                  </div>
                  <Button type="submit" className="mt-3 w-full" disabled={isEmailLoading}>
                    {isEmailLoading ? 'Sending...' : 'Send Code'}
                  </Button>
                </form>
              ) : (
                <form onSubmit={handleLoginWithCode}>
                  <div className="grid gap-3">
                    <Label htmlFor="code">Verification Code</Label>
                    <p className="text-muted-foreground mb-2 text-sm">
                      We sent a 6-digit code to {email}
                    </p>
                    <Input
                      id="code"
                      type="text"
                      placeholder="Enter 6-digit code"
                      value={code}
                      onChange={e => setCode(e.target.value)}
                      required
                      disabled={isEmailLoading}
                      maxLength={6}
                    />
                  </div>
                  <Button type="submit" className="mt-3 w-full" disabled={isEmailLoading}>
                    {isEmailLoading ? 'Verifying...' : 'Verify Code'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="mt-2 w-full"
                    onClick={() => {
                      // Reset local state
                      setCode('');
                      setEmail('');
                      // Force re-render to reset email flow
                      setEmailFlowKey(prev => prev + 1);
                    }}
                    disabled={isEmailLoading}
                  >
                    Back to Email
                  </Button>
                </form>
              )}
            </div>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                type="button"
                onClick={() => handleOAuthLogin('google')}
                disabled={isOAuthLoading || isEmailLoading}
                title="Continue with Google"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="size-4">
                  <path
                    d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                    fill="currentColor"
                  />
                </svg>
              </Button>

              <Button
                variant="outline"
                type="button"
                onClick={() => handleOAuthLogin('twitter')}
                disabled={isOAuthLoading || isEmailLoading}
                title="Continue with X"
              >
                <XIcon className="size-4" />
              </Button>

              <Button
                variant="outline"
                type="button"
                onClick={() => handleOAuthLogin('discord')}
                disabled={isOAuthLoading || isEmailLoading}
                title="Continue with Discord"
              >
                <DiscordIcon className="size-4" />
              </Button>
            </div>

            <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
              <span className="bg-background text-muted-foreground relative z-10 px-2">Or</span>
            </div>

            <Button
              variant="outline"
              type="button"
              className="w-full"
              onClick={handleWalletLogin}
              disabled={disableWalletLogin || isEmailLoading || isOAuthLoading}
            >
              <WalletIcon className="size-4" />
              Connect Wallet
            </Button>
          </div>
          <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
            By clicking continue, you agree to our <a href="#">Terms of Service</a> and{' '}
            <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
