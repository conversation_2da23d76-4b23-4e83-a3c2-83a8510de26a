import { useState, useEffect, useRef } from 'react';
import { DataTable } from '@/components/data-table';
import { useGeckoTerminal } from '@/hooks/useGeckoTerminal';
import { useMediaQuery } from 'react-responsive';
import { createColumns } from './columns';
import Pagination from '@/components/pagination';
import TableNoData from '@/components/table-nodata';
import TableLoading from '@/components/table-loading';
import useMarketHyperEvmTabStore from '@/store/marketHyperEvmTab';

const HyperEvmDexesScreen = () => {
    const { currentTab } = useMarketHyperEvmTabStore();
    const { useTrendingPools, useTopPoolsByDex } = useGeckoTerminal();
    const [page, setPage] = useState(1);

    // Lưu trữ tab trước đó để reset trang khi tab thay đổi
    const prevTabRef = useRef(currentTab);

    // Reset về trang 1 khi tab thay đổi
    useEffect(() => {
        if (prevTabRef.current !== currentTab) {
            setPage(1);
            prevTabRef.current = currentTab;
        }
    }, [currentTab]);

    // Sử dụng hook khác nhau dựa trên currentTab
    const isTrendingTab = currentTab === 'trending';

    // Query cho trending tab
    const trendingQuery = useTrendingPools('hyperevm', page);

    // Query cho các tab khác (dex)
    const dexQuery = useTopPoolsByDex(
        'hyperevm',
        isTrendingTab ? '' : currentTab, // Không cần thiết khi đang ở trending tab
        page,
        'h24_tx_count_desc'
    );

    // Quyết định sử dụng kết quả từ query nào
    const { data, isLoading } = isTrendingTab ? trendingQuery : dexQuery;
    const dataResponse = data?.data;

    const [isLastPage, setIsLastPage] = useState(false);
    const isMobile = useMediaQuery({ maxWidth: 768 });
    const isTablet = useMediaQuery({ minWidth: 769, maxWidth: 1024 });

    // Check if next page has data - cũng cần phân biệt giữa trending và dex
    const nextPageTrendingQuery = useTrendingPools('hyperevm', page + 1);
    const nextPageDexQuery = useTopPoolsByDex(
        'hyperevm',
        isTrendingTab ? '' : currentTab,
        page + 1,
        'h24_tx_count_desc'
    );

    const nextPageData = isTrendingTab ? nextPageTrendingQuery.data : nextPageDexQuery.data;

    // Update isLastPage when nextPageData changes
    useEffect(() => {
        if (nextPageData?.data && nextPageData.data.length === 0) {
            setIsLastPage(true);
        } else {
            setIsLastPage(false);
        }
    }, [nextPageData]);

    const handlePreviousPage = () => {
        if (page > 1) {
            setPage(page - 1);
        }
    };

    const handleNextPage = () => {
        if (!isLastPage) {
            setPage(page + 1);
        }
    };

    const allColumns = createColumns();

    // Filter columns based on device type
    let columns;
    if (isMobile) {
        // Mobile: show only Token, Price, and 24H Change columns
        columns = allColumns.filter((_, index) => index === 0 || index === 1 || index === 5);
    } else if (isTablet) {
        // Tablet: show Token, Price, Market Cap, 24H Change, and Volume
        columns = allColumns.filter((_, index) => index === 0 || index === 1 || index === 2 || index === 5 || index === 6);
    } else {
        // Desktop: show all columns
        columns = allColumns;
    }

    // Hiển thị trạng thái loading khi changing tabs hoặc fetching
    if (isLoading) return <TableLoading />;
    if (!dataResponse) return <TableNoData />

    return (
        <div className="pb-safe pb-28 w-full">
            <DataTable columns={columns} data={dataResponse} />
            <div className="flex items-center justify-center md:justify-center space-y-3 py-4 sm:space-y-0 sm:space-x-2">
                <Pagination
                    currentPage={page}
                    goToNextPage={handleNextPage}
                    goToPrevPage={handlePreviousPage}
                    disabled={isLastPage}
                />
            </div>
        </div>
    );
};

export default HyperEvmDexesScreen;