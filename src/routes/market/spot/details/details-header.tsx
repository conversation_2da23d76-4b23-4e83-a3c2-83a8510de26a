import { ChevronLeftIcon } from "lucide-react"
import { useNavigate } from "react-router"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/utils/formatters"
import { getSpotTokenImage } from "@/utils/hyperliquidUtils"
import ShareBtn from "@/components/share-btn"
import TravelExplorer from "@/assets/icons/TravelExplorer"

interface DetailsHeaderProps {
    symbol: string;
    priceInfo: {
        markPrice: number;
        previousDayPrice: number;
        dayVolume: number;
    };
    isUp: boolean;
    isDown: boolean;
    formattedPercentChange: number;
    tokenAddress: string;
}

const DetailsHeader = ({ symbol, tokenAddress, priceInfo, isUp, isDown, formattedPercentChange }: DetailsHeaderProps) => {
    const navigate = useNavigate();
    return (
        <div className="flex justify-between md:justify-start items-center gap-4 sticky top-0 left-0 right-0 z-50 border-b border-border backdrop-blur p-2">
            <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                    <button className="py-2 px-2" onClick={() => navigate(-1)}>
                        <ChevronLeftIcon />
                    </button>
                    <div className="flex items-center gap-2">
                        <img
                            src={getSpotTokenImage(symbol)}
                            alt={symbol}
                            className="size-8"
                            onError={e => {
                                e.currentTarget.style.display = 'none';
                                const parent = e.currentTarget.parentElement;
                                if (parent) {
                                    const fallbackDiv = document.createElement('div');
                                    fallbackDiv.className =
                                        'size-8 bg-primary/10 flex items-center justify-center font-bold text-primary';
                                    fallbackDiv.textContent = symbol.charAt(0).toUpperCase();
                                    parent.insertBefore(fallbackDiv, e.currentTarget);
                                }
                            }}
                        />
                        <h1 className="text-2xl font-bold">{symbol}</h1>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    <p className="text-xl font-semibold">{formatCurrency(priceInfo.markPrice)}</p>
                    <p className={cn("text-sm font-semibold px-2 py-1 rounded-full", {
                        "text-green-500 bg-green-500/10": isUp,
                        "text-red-500 bg-red-500/10": isDown,
                    })}>{isUp ? '+' : ''}{formattedPercentChange.toFixed(2)}%</p>
                </div>
            </div>


            <div className="flex items-center gap-4">
                <ShareBtn />
                <a href={`https://hypurrscan.io/token/${tokenAddress}`} target="_blank" rel="noopener noreferrer">
                    <TravelExplorer className="size-6" />
                </a>
            </div>
            {symbol === 'PURRO' && (
                <div className="bg-primary text-white p-2 rounded-full animate-pulse hidden md:block">
                    Support PURRO!!!
                </div>
            )}
            {/* <div className="hidden md:flex items-center gap-4">
                <div>
                    <p className="text-sm font-semibold">Price</p>
                    <p className="text-sm font-semibold">{formatCurrency(priceInfo.markPrice)}</p>
                </div>
                <div>
                    <p className="text-sm font-semibold">24h Change</p>
                    <p className={cn("text-sm", {
                        "text-green-500": isUp,
                        "text-red-500": isDown,
                    })}>{isUp ? '+' : ''}{formattedPercentChange.toFixed(2)}%</p>
                </div>
                <div>
                    <p className="text-sm font-semibold">24h Volume</p>
                    <p className="text-sm font-semibold">{formatCurrency(priceInfo.dayVolume)}</p>
                </div>
            </div> */}
        </div>
    )
}

export default DetailsHeader