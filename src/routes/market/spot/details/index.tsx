import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router';
import PoolInfo from './pool-info';
import useSpotAssetContext from '@/hooks/useSpotAssetContext';
import useHyperliquidApi from '@/hooks/useHyperliquidApi';
import TimeframeSelector from '@/components/charts/timeframe-selector';
import DetailsHeader from './details-header';
import TokenSimpleChart from '@/components/charts/token-simple-chart';
import { useGeckoTerminal } from '@/hooks/useGeckoTerminal';

// Define candle data type
interface CandleData {
    t: number;  // timestamp
    T: number;  // end timestamp
    s: string;  // symbol
    i: string;  // interval
    o: string;  // open
    c: string;  // close
    h: string;  // high
    l: string;  // low
    v: string;  // volume
    n: number;  // number of trades
}

// Define chart data type
interface ChartDataPoint {
    time: string;
    price: number;
    timestamp: number;
    open: number;
    high: number;
    low: number;
    volume: number;
}

const SpotDetailsScreen = () => {
    const params = useParams();
    const symbol = params.symbol;
    const { spotIndexer } = useSpotAssetContext(3000);
    const { useCandleSnapshot } = useHyperliquidApi();
    const { useTopPoolsForAToken } = useGeckoTerminal();
    const [timeFrame, setTimeFrame] = useState<'1h' | '24h' | '1w' | '1m' | 'ytd' | 'all'>('24h');

    // Get spot info if symbol exists
    const spotInfo = symbol ? spotIndexer?.getSpotInfo([symbol]) : null;

    // Use the hook at the top level with proper conditional enabling
    const universe = symbol && spotInfo && spotInfo[symbol] ? spotInfo[symbol].universe : '';
    const { data: candleData } = useCandleSnapshot(universe || '', timeFrame);

    const { data: topPoolsForAToken } = useTopPoolsForAToken('hyperliquid', spotInfo && spotInfo[symbol as keyof typeof spotInfo] ? spotInfo[symbol as keyof typeof spotInfo].tokenId : '');

    // Extract pool data for rendering
    const poolData = topPoolsForAToken?.data?.[0]?.attributes;

    // Calculate chart domain for better visualization
    const chartDomain = React.useMemo(() => {
        if (!candleData || candleData.length === 0) return [0, 0];

        // Find min and max prices
        let minPrice = Infinity;
        let maxPrice = -Infinity;

        candleData.forEach((candle: CandleData) => {
            const close = parseFloat(candle.c);
            if (close < minPrice) minPrice = close;
            if (close > maxPrice) maxPrice = close;
        });

        // Calculate a narrower range to make the chart more dynamic
        // Use a small percentage of the price range as padding
        const range = maxPrice - minPrice;
        const padding = range * 0.1; // 10% padding

        return [
            minPrice - padding, // min domain with padding
            maxPrice + padding  // max domain with padding
        ];
    }, [candleData]);

    // Define formatCandleData inside useMemo to avoid dependency issues
    const chartData = React.useMemo(() => {
        const formatData = (data: CandleData[] | undefined): ChartDataPoint[] => {
            if (!data || data.length === 0) return [];

            return data.map(candle => {
                // Format timestamp based on the selected timeframe
                const date = new Date(candle.t);
                let formattedTime = '';

                // Format time based on timeframe
                switch (timeFrame) {
                    case '1h': {
                        // For 1h, show minutes (HH:MM)
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        formattedTime = `${hours}:${minutes}`;
                        break;
                    }
                    case '24h': {
                        // For 24h, show hours (HH:MM)
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        formattedTime = `${hours}:${minutes}`;
                        break;
                    }
                    case '1w': {
                        // For 1w, show day of week
                        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                        formattedTime = days[date.getDay()];
                        break;
                    }
                    case '1m':
                    case 'ytd': {
                        // For 1m and ytd, show MM/DD
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');
                        formattedTime = `${month}/${day}`;
                        break;
                    }
                    case 'all': {
                        // For all, show MM/YY
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear().toString().substring(2);
                        formattedTime = `${month}/${year}`;
                        break;
                    }
                    default: {
                        // Default format MM/DD
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const day = date.getDate().toString().padStart(2, '0');
                        formattedTime = `${month}/${day}`;
                    }
                }

                return {
                    time: formattedTime,
                    price: parseFloat(candle.c), // Close price
                    timestamp: candle.t,
                    open: parseFloat(candle.o),
                    high: parseFloat(candle.h),
                    low: parseFloat(candle.l),
                    volume: parseFloat(candle.v)
                };
            });
        };

        return formatData(candleData) || [];
    }, [candleData, timeFrame]);

    const isUpTrend = useMemo(() => {
        if (chartData.length < 2) return true; // mặc định xanh

        const firstPrice = chartData[0].price;
        const lastPrice = chartData[chartData.length - 1].price;

        return lastPrice >= firstPrice;
    }, [chartData]);

    // Early return if no symbol
    if (!symbol) return null;

    // const tokenId = spotInfo ? spotInfo[symbol].tokenId : null;
    const priceInfo = spotInfo ? spotInfo[symbol].priceInfo : null;
    if (!priceInfo) return null;
    const percentChange = priceInfo.markPrice / priceInfo.previousDayPrice - 1;
    const formattedPercentChange = percentChange * 100;
    const isUp = formattedPercentChange > 0;
    const isDown = formattedPercentChange < 0;

    return (
        <main className="h-full md:mb-0 mb-20">
            <DetailsHeader
                symbol={symbol}
                priceInfo={priceInfo}
                isUp={isUp}
                isDown={isDown}
                formattedPercentChange={formattedPercentChange}
                tokenAddress={spotInfo ? spotInfo[symbol].tokenId : ''}
            />
            <div className='grid grid-cols-4'>
                <div className="md:col-span-3 col-span-4 border-r border-b flex flex-col-reverse md:flex-col">
                    <div className="p-2 flex items-center justify-center md:justify-start">
                        <TimeframeSelector
                            timeFrame={timeFrame}
                            onChange={setTimeFrame}
                        />
                    </div>
                    <div className="h-[60vh] w-full border-t">
                        <TokenSimpleChart
                            chartData={chartData}
                            chartDomain={chartDomain}
                            isUpTrend={isUpTrend}
                        />
                    </div>
                </div>
                {poolData &&
                    <div className="col-span-4 md:col-span-1 ">
                        <PoolInfo poolData={poolData} />
                    </div>}
            </div>
        </main >
    )
}

export default SpotDetailsScreen