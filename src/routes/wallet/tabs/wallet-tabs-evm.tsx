import TabsError from './tabs-error';
import TabsLoading from './tabs-loading';
import { usePortfolioData } from '@/hooks/usePortfolioData';
import { useAccount } from 'wagmi';

interface EvmToken {
  token: {
    address: string;
    name: string;
    symbol: string;
    decimals: string;
    icon_url: string | null;
  };
  value: string;
}

interface TokenPricesData {
  [key: string]: string;
}

const calculateTokenValue = (
  tokenAmount: string,
  decimals: string,
  tokenPrice: string | undefined
): string => {
  if (!tokenPrice) {
    return '0.00';
  }

  const tokenValue =
    (parseFloat(tokenAmount) / Math.pow(10, parseFloat(decimals))) * parseFloat(tokenPrice);

  return tokenValue.toFixed(2);
};

const WalletTabsEVM = () => {
  const { address } = useAccount();
  const { evmData, isEvmLoading, evmError, evmValue } = usePortfolioData(address || '');

  // Safely access token prices data
  const tokenPricesData: TokenPricesData = evmData?.tokenPricesData || {};

  // Calculate total balance - using evmValue which is already calculated in the hook
  const totalBalance = evmValue || 0;

  // Sort tokens by value (highest to lowest)
  const sortedItems = evmData?.tokensData?.items
    ? [...evmData.tokensData.items].sort((a: EvmToken, b: EvmToken) => {
      const valueA = parseFloat(a.value) / Math.pow(10, parseFloat(a.token.decimals));
      const valueB = parseFloat(b.value) / Math.pow(10, parseFloat(b.token.decimals));

      const addressA = a.token.address.toLowerCase();
      const addressB = b.token.address.toLowerCase();

      const priceA =
        tokenPricesData && addressA in tokenPricesData
          ? parseFloat(tokenPricesData[addressA])
          : 0;
      const priceB =
        tokenPricesData && addressB in tokenPricesData
          ? parseFloat(tokenPricesData[addressB])
          : 0;

      const totalValueA = valueA * priceA;
      const totalValueB = valueB * priceB;

      return totalValueB - totalValueA; // Sort from highest to lowest
    })
    : [];

  if (isEvmLoading) return <TabsLoading />;

  if (evmError) return <TabsError message="Error fetching tokens, please try again later" />;

  return (
    <div className="space-y-6 px-2">
      {/* Account Summary */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-2 md:grid-cols-2">
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Total Balance</div>
            <div className="font-semibold text-lg">${totalBalance.toFixed(2)}</div>
          </div>
          <div className="bg-card rounded-lg p-3">
            <div className="text-muted-foreground text-sm">Tokens</div>
            <div className="font-semibold text-lg">{evmData?.tokensData?.items?.length || 0}</div>
          </div>
        </div>
      </div>

      {/* Token List */}
      <div>
        {sortedItems && sortedItems.length > 0 ? (
          <div className="space-y-2 overflow-visible">
            {sortedItems.map((item: EvmToken, index: number) => {
              const value = parseFloat(item.value) / Math.pow(10, parseFloat(item.token.decimals));
              const tokenAddress = item.token.address.toLowerCase();
              const tokenPrice =
                tokenPricesData && tokenAddress in tokenPricesData
                  ? tokenPricesData[tokenAddress]
                  : undefined;

              return (
                <div key={index} className="bg-card rounded-lg p-3">
                  <div className="mb-2 flex justify-between">
                    <div className="min-w-0 flex-1 overflow-hidden">
                      <div className="truncate font-semibold">
                        {item.token.symbol}
                        <span className="text-muted-foreground ml-2 hidden text-xs font-normal sm:inline">
                          {item.token.name}
                        </span>
                      </div>
                      <div className="text-muted-foreground mt-0.5 truncate text-xs">
                        {value.toFixed(6)} {item.token.symbol}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ${calculateTokenValue(item.value, item.token.decimals, tokenPrice)}
                      </div>
                      <div className="text-muted-foreground mt-0.5 truncate text-xs">
                        {item.token.address.substring(0, 6)}...
                        {item.token.address.substring(item.token.address.length - 4)}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="bg-card text-muted-foreground rounded-lg p-3 text-center">
            No tokens found
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletTabsEVM;
