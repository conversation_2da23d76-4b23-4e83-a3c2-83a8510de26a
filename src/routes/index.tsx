import { Routes, Route, Navigate } from 'react-router';
import { AuthLayout, MarketLayout, WalletLayout } from '@/layouts';
import ExploreScreen from './explore';
import SpotScreen from './market/spot';
import HyperEvmLayout from './market/hyperevm/hyperevm-layout';
import Settings from './settings';
import Terms from './terms';
import Privacy from './privacy';
import FuturesScreen from './futures';
import MobileSettingsScreen from './mobile-settings';
import SpotTradeScreen from './trade/spot';
import HyperEvmDexesScreen from './market/hyperevm/hyperevm-dexes';
import WalletScreen from './wallet';
import MarketRedirect from './market/market-redirect';
import SpotDetailsScreen from './market/spot/details';
import Notifications from './mobile-settings/notifications';
import Trade from './trade';
import CampaignScreen from './campaign';
import Appearance from './mobile-settings/appearance';
import LoginPage from './login/page';

export default function AppRoutes() {
  return (
    <Routes>
      <Route element={<AuthLayout />}>
        <Route path="/" element={<WalletLayout />} >
          <Route index element={<WalletScreen />} />
        </Route>
        <Route path="/market" element={<MarketLayout />}>
          <Route index element={<MarketRedirect />} />
          <Route path="spot" element={<SpotScreen />} />
          <Route path="hyperevm" element={<HyperEvmLayout />} >
            <Route index element={<HyperEvmDexesScreen />} />
          </Route>
        </Route>
        <Route path="/market/spot/:symbol" element={<SpotDetailsScreen />} />
        <Route path="/campaign" element={<CampaignScreen />} />
        <Route path="/trade" element={<Trade />} />
        <Route path="/trade/spot/:tokenId" element={<SpotTradeScreen />} />
        <Route path="/explore" element={<ExploreScreen />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/futures" element={<FuturesScreen />} />
      </Route>
      <Route path="/mobile-settings" element={<MobileSettingsScreen />} />
      <Route path="/mobile-settings/notifications" element={<Notifications />} />
      <Route path="/mobile-settings/appearance" element={<Appearance />} />
      <Route path="/login" element={<LoginPage />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />
      <Route path="*" element={<Navigate to="/" />} />
    </Routes>
  );
}
