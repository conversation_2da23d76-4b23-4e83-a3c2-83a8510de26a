import { useMfaEnrollment } from "@privy-io/react-auth";
import { Button } from "@/components/ui/button";
import { Lock } from "lucide-react";
import { usePrivy } from "@privy-io/react-auth";

const Security = () => {
    const { showMfaEnrollmentModal } = useMfaEnrollment();
    const { authenticated } = usePrivy();
    return (
        <div className="w-full px-4 space-y-6">
            <h2 className="text-2xl font-bold mb-4">Security</h2>
            {authenticated ? (
                <Button onClick={() => showMfaEnrollmentModal()}><Lock /> Enroll MFA</Button>
            ) : (
                <p className="text-muted-foreground">Connect wallet to enable security features.</p>
            )}
        </div>
    );
};

export default Security