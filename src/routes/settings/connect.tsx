import { ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import XIcon from '@/assets/icons/XIcon';
import TelegramIcon from '@/assets/icons/TelegramIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';

const Connect = () => {
    // Social media URLs - replace with actual URLs for Purro Wallet
    const socialLinks = {
        twitter: 'https://x.com/purro_xyz',
        telegram: 'https://t.me/purro_xyz',
        discord: 'https://discord.gg/VJunuK9T5w'
    };

    const openSocialLink = (url: string) => {
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    return (
        <div className="w-full px-4 space-y-6">
            <h2 className="text-2xl font-bold mb-4">Social Media</h2>

            <div className="grid gap-4">
                <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center gap-3">
                        <XIcon />
                        <div>
                            <h3 className="font-medium">X</h3>
                            <p className="text-sm text-muted-foreground">Latest updates</p>
                        </div>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openSocialLink(socialLinks.twitter)}
                    >
                        <ExternalLink className="size-4" />
                    </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center gap-3">
                        <TelegramIcon />
                        <div>
                            <h3 className="font-medium">Telegram</h3>
                            <p className="text-sm text-muted-foreground">Community support</p>
                        </div>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openSocialLink(socialLinks.telegram)}
                    >
                        <ExternalLink className="size-4" />
                    </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center gap-3">
                        <DiscordIcon />
                        <div>
                            <h3 className="font-medium">Discord</h3>
                            <p className="text-sm text-muted-foreground">Community discussions</p>
                        </div>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openSocialLink(socialLinks.discord)}
                    >
                        <ExternalLink className="size-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default Connect;