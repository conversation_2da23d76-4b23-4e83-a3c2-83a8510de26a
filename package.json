{"name": "purro-app", "private": true, "version": "0.9.7", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-pwa-assets": "pwa-assets-generator", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@dynamic-labs/ethereum": "^4.19.3", "@dynamic-labs/global-wallet": "^4.19.3", "@dynamic-labs/sdk-api": "^0.0.678", "@dynamic-labs/sdk-react-core": "^4.19.3", "@dynamic-labs/wagmi-connector": "^4.19.3", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@privy-io/react-auth": "^2.13.6", "@privy-io/wagmi": "^1.0.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.79.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-responsive": "^10.0.1", "react-router": "^7.6.1", "recharts": "^2.15.3", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "vaul": "^1.1.2", "viem": "^2.30.5", "wagmi": "^2.15.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/serviceworker": "^0.0.136", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react-swc": "^3.10.0", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tw-animate-css": "^1.3.2", "typescript": "~5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0"}}